<template>
  <div id="digital-flop">
    <div
      class="digital-flop-item"
      v-for="item in digitalFlopData"
      :key="item.title"
    >
      <div class="card-header">
        <div class="card-icon">
          <i :class="item.icon"></i>
        </div>
        <div class="card-title">{{ item.title }}</div>
      </div>

      <div class="card-content">
        <!-- 单主数据布局 -->
        <div v-if="item.mainLabel" class="main-data-row">
          <span class="main-label">{{ item.mainLabel }}</span>
          <div class="main-value">
            <dv-digital-flop
              :config="item.mainNumber"
              style="width:120px;height:60px;"
            />
            <span class="main-unit">{{ item.mainUnit }}</span>
          </div>
        </div>

        <!-- 双主数据布局 -->
        <div v-if="item.dualMainData" class="dual-main-data">
          <div class="dual-main-item" v-for="mainItem in item.dualMainData" :key="mainItem.label">
            <div class="dual-main-number">
              <dv-digital-flop
                :config="mainItem.number"
                style="width:100px;height:50px;"
              />
            </div>
            <div class="dual-main-label">{{ mainItem.label }}</div>
          </div>
        </div>

        <div class="sub-data-list" v-if="item.subData">
          <div class="sub-data-row" v-for="sub in item.subData" :key="sub.label">
            <span class="sub-label">{{ sub.label }}</span>
            <div class="sub-value">
              <span v-if="sub.staticValue" class="static-value">{{ sub.staticValue }}</span>
              <template v-else>
                <dv-digital-flop
                  :config="sub.number"
                  style="width:60px;height:30px;"
                />
                <span class="sub-unit">{{ sub.unit }}</span>
              </template>
            </div>
          </div>
        </div>

        <div class="progress-section" v-if="item.progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: item.progress + '%' }"></div>
          </div>
          <div class="progress-text">{{ item.progressLabel }}: {{ item.progress }}%</div>
        </div>

        <div class="notice-list" v-if="item.notices">
          <div class="notice-item" v-for="notice in item.notices" :key="notice.title">
            <div class="notice-title">{{ notice.title }}</div>
            <div class="notice-date">{{ notice.date }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DigitalFlop',
  data () {
    return {
      digitalFlopData: []
    }
  },
  methods: {
    createData () {
      this.digitalFlopData = [
        {
          title: '教师情况',
          icon: 'fas fa-user-tie',
          mainLabel: '教师人数',
          mainNumber: {
            number: [715],
            content: '{nt}',
            textAlign: 'right',
            style: {
              fill: '#333',
              fontWeight: 'bold',
              fontSize: '48px'
            }
          },
          mainUnit: '人',
          subData: [
            {
              label: '请假',
              number: {
                number: [10],
                content: '{nt}',
                textAlign: 'right',
                style: {
                  fill: '#666',
                  fontWeight: 'normal',
                  fontSize: '20px'
                }
              },
              unit: '人'
            },
            {
              label: '公差',
              number: {
                number: [11],
                content: '{nt}',
                textAlign: 'right',
                style: {
                  fill: '#666',
                  fontWeight: 'normal',
                  fontSize: '20px'
                }
              },
              unit: '人'
            }
          ],
          progress: 96,
          progressLabel: '在校率'
        },
        {
          title: '学生情况',
          icon: 'fas fa-user-graduate',
          dualMainData: [
            {
              number: {
                number: [4450],
                content: '{nt}',
                textAlign: 'center',
                style: {
                  fill: '#333',
                  fontWeight: 'bold',
                  fontSize: '36px'
                }
              },
              label: '应在校学生'
            },
            {
              number: {
                number: [4285],
                content: '{nt}',
                textAlign: 'center',
                style: {
                  fill: '#333',
                  fontWeight: 'bold',
                  fontSize: '36px'
                }
              },
              label: '实际在校'
            }
          ],
          subData: [
            {
              label: '学生出校人数',
              number: {
                number: [165],
                content: '{nt}',
                textAlign: 'right',
                style: {
                  fill: '#666',
                  fontWeight: 'normal',
                  fontSize: '20px'
                }
              },
              unit: '人'
            },
            {
              label: '校外人数',
              number: {
                number: [28],
                content: '{nt}',
                textAlign: 'right',
                style: {
                  fill: '#666',
                  fontWeight: 'normal',
                  fontSize: '20px'
                }
              },
              unit: '人'
            }
          ],
          progress: 96.3,
          progressLabel: '在校率'
        },
        {
          title: '今日进出门次',
          icon: 'fas fa-door-open',
          dualMainData: [
            {
              number: {
                number: [1245],
                content: '{nt}',
                textAlign: 'center',
                style: {
                  fill: '#4a90e2',
                  fontWeight: 'bold',
                  fontSize: '36px'
                }
              },
              label: '进门次'
            },
            {
              number: {
                number: [1180],
                content: '{nt}',
                textAlign: 'center',
                style: {
                  fill: '#4a90e2',
                  fontWeight: 'bold',
                  fontSize: '36px'
                }
              },
              label: '出门次'
            }
          ],
          subData: [
            {
              label: '高峰时段',
              staticValue: '16:00-17:00'
            },
            {
              label: '当前在校',
              number: {
                number: [4285],
                content: '{nt}',
                textAlign: 'right',
                style: {
                  fill: '#666',
                  fontWeight: 'normal',
                  fontSize: '20px'
                }
              },
              unit: '人'
            }
          ]
        },
        {
          title: '通知公告',
          icon: 'fas fa-bell',
          notices: [
            { title: '欢迎新学期！', date: '2024-08-12' },
            { title: '门禁系统升级通知', date: '2024-08-10' },
            { title: '安全管理规定', date: '2024-08-08' }
          ]
        }
      ]
    }
  },
  mounted () {
    const { createData } = this

    createData()

    setInterval(createData, 30000)
  }
}
</script>

<style lang="less">
#digital-flop {
  position: relative;
  height: 20%;
  min-height: 200px;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 20px;
  padding: 15px;
  background: transparent;

  .digital-flop-item {
    flex: 1;
    background: #c8d0e0;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 180px;
  }

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 25px;

    .card-icon {
      width: 32px;
      height: 32px;
      background: #333;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;

      i {
        font-size: 16px;
        color: #fff;
      }
    }

    .card-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
  }

  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .main-data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;

    .main-label {
      font-size: 16px;
      color: #666;
      font-weight: normal;
    }

    .main-value {
      display: flex;
      align-items: baseline;

      .main-unit {
        font-size: 24px;
        color: #333;
        font-weight: bold;
        margin-left: 8px;
      }
    }
  }

  .dual-main-data {
    display: flex;
    justify-content: space-around;
    margin-bottom: 25px;
    padding-bottom: 15px;

    .dual-main-item {
      text-align: center;
      flex: 1;

      .dual-main-number {
        margin-bottom: 8px;
      }

      .dual-main-label {
        font-size: 14px;
        color: #666;
        font-weight: normal;
      }
    }
  }

  .sub-data-list {
    margin-bottom: 20px;

    .sub-data-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .sub-label {
        font-size: 14px;
        color: #666;
        font-weight: normal;
      }

      .sub-value {
        display: flex;
        align-items: baseline;

        .sub-unit {
          font-size: 14px;
          color: #666;
          margin-left: 4px;
        }

        .static-value {
          font-size: 14px;
          color: #666;
          font-weight: normal;
        }
      }
    }
  }

  .progress-section {
    margin-top: auto;

    .progress-bar {
      height: 8px;
      background: #333;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 8px;

      .progress-fill {
        height: 100%;
        background: #333;
        border-radius: 4px;
        transition: width 0.3s ease;
      }
    }

    .progress-text {
      font-size: 12px;
      color: #666;
      text-align: left;
    }
  }

  .notice-list {
    .notice-item {
      background: rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      padding: 12px 15px;
      margin-bottom: 10px;

      .notice-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 4px;
        line-height: 1.4;
        font-weight: 500;
      }

      .notice-date {
        font-size: 12px;
        color: #999;
      }
    }
  }
}
</style>
