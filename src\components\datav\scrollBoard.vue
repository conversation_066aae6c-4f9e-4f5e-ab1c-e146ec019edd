<template>
  <div id="scroll-board">
    <dv-scroll-board :config="config" />
  </div>
</template>

<script>
export default {
  name: 'ScrollBoard',
  data () {
    return {
      config: {
        header: ['序号', '学生姓名', '学院', '天数'],
        data: [
          ['1', '张三', '电子信息工程学院', '3天'],
          ['2', '李四', '机械电气工程学院', '2天'],
          ['3', '王五', '石油化工学院', '1天'],
          ['4', '赵六', '旅游与商务管理学院', '4天'],
          ['5', '钱七', '建筑工程学院', '2天'],
          ['6', '孙八', '电子信息工程学院', '5天'],
          ['7', '周九', '机械电气工程学院', '1天'],
          ['8', '吴十', '石油化工学院', '3天'],
          ['9', '郑一', '旅游与商务管理学院', '2天'],
          ['10', '冯二', '建筑工程学院', '4天']
        ],
        index: true,
        columnWidth: [80, 120, 200, 100],
        align: ['center'],
        rowNum: 7,
        headerBGC: '#1981f6',
        headerHeight: 45,
        oddRowBGC: 'rgba(0, 44, 81, 0.8)',
        evenRowBGC: 'rgba(10, 29, 50, 0.8)'
      }
    }
  }
}
</script>

<style lang="less">
#scroll-board {
  width: 50%;
  box-sizing: border-box;
  margin-left: 20px;
  height: 100%;
  overflow: hidden;
}
</style>
